import { Plus, Trash2 } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { type Editor, RichTextEditor } from "@/components/RichTextEditor";
import { But<PERSON> } from "@/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import type { TableCell, TableProperties } from "@/types/table";
import type { TestDataRecord } from "@/utils/apiService";
import { processContentWithVariables } from "@/utils/contentProcessing";
import {
	calculateScaledDimensions,
	cellToLogicalPosition,
	createLogicalGrid,
	expandSelectionForMergedCells,
	isCellSelectedInLogicalGrid,
	logicalPositionToCell,
} from "@/utils/tableUtils";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import { TableBodyContent } from "./Table";

interface TableEditorProps {
	tableProperties: TableProperties;
	onChange: (properties: TableProperties) => void;
	setActiveEditor: (editor: Editor | null) => void; // Add this prop
	containerWidthMm?: number;
	containerHeightMm?: number;
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
	setIsTextEditorFocused?: (focused: boolean) => void;
}

// Helper to compare selection objects by value
const areSelectionsEqual = (
	selA: TableProperties["selection"],
	selB: TableProperties["selection"],
): boolean => {
	if (selA === selB) return true; // Handles if both are null or same reference
	if (!selA || !selB) return false; // One is null, the other isn't
	return (
		selA.start.row === selB.start.row &&
		selA.start.col === selB.start.col &&
		selA.end.row === selB.end.row &&
		selA.end.col === selB.end.col
	);
};

export function TableEditor({
	tableProperties,
	onChange,
	setActiveEditor,
	containerWidthMm,
	containerHeightMm,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
	setIsTextEditorFocused,
}: TableEditorProps) {
	const [resizingCol, setResizingCol] = useState<number | null>(null);
	const [resizingRow, setResizingRow] = useState<number | null>(null);
	const [selectedCells, setSelectedCells] =
		useState<TableProperties["selection"]>(null);
	const [isShiftPressed, setIsShiftPressed] = useState(false);

	// Track the logical cursor position for navigation (separate from expanded selection)
	// This represents the actual cell position we're navigating from, not the expanded selection
	const [logicalCursorPosition, setLogicalCursorPosition] = useState<{
		row: number;
		col: number;
	} | null>(null);
	const [isDragging, setIsDragging] = useState(false);
	const [columnWidths, setColumnWidths] = useState<number[]>([]);
	const [rowHeights, setRowHeights] = useState<number[]>([]);
	const [isResizing, setIsResizing] = useState(false);
	const [editingCell, setEditingCell] = useState<{
		row: number;
		col: number;
	} | null>(null);
	const [intrinsicColumnWidths, setIntrinsicColumnWidths] = useState<number[]>(
		[],
	);
	const [intrinsicRowHeights, setIntrinsicRowHeights] = useState<number[]>([]);
	const startPosRef = useRef({ x: 0, y: 0 });
	const tableRef = useRef<HTMLTableElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const tableWidthRef = useRef<number>(0);
	const resizeIndicatorRef = useRef<HTMLDivElement | null>(null);
	const initialSizeRef = useRef<{ width?: number; height?: number }>({});

	// Minimum dimensions in mm
	const minWidthMm = 2;
	const minHeightMm = 2;

	const handleResizeStart = (
		e: React.MouseEvent,
		index: number,
		type: "col" | "row",
	) => {
		e.preventDefault();
		e.stopPropagation(); // Prevent selection handling
		setIsResizing(true);

		// Store initial size
		if (type === "col") {
			setResizingCol(index);
			initialSizeRef.current.width = intrinsicColumnWidths[index] || 15;
			// Highlight entire column
			const rows = tableRef.current?.getElementsByTagName("tr");
			if (rows) {
				for (let i = 0; i < rows.length; i++) {
					const cell = rows[i].getElementsByTagName("td")[index];
					if (cell) cell.classList.add("bg-blue-50");
				}
			}

			// Create visual resize indicator
			if (!resizeIndicatorRef.current && containerRef.current) {
				const indicator = document.createElement("div");
				indicator.className = "absolute bg-blue-500 pointer-events-none z-50";
				indicator.style.width = "2px";
				indicator.style.top = "0";
				indicator.style.bottom = "0";
				containerRef.current.appendChild(indicator);
				resizeIndicatorRef.current = indicator;
			}

			// Position the indicator at the exact mouse location on mousedown
			if (resizeIndicatorRef.current && containerRef.current) {
				const containerRect = containerRef.current.getBoundingClientRect();
				resizeIndicatorRef.current.style.left = `${e.clientX - containerRect.left}px`;
			}
		} else {
			setResizingRow(index);
			initialSizeRef.current.height = intrinsicRowHeights[index] || 10;
			// Highlight entire row
			const row = tableRef.current?.getElementsByTagName("tr")[index];
			if (row) {
				const cells = row.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.add("bg-blue-50");
				}
			}

			// Create visual resize indicator
			if (!resizeIndicatorRef.current && containerRef.current) {
				const indicator = document.createElement("div");
				indicator.className = "absolute bg-blue-500 pointer-events-none z-50";
				indicator.style.height = "2px";
				indicator.style.left = "0";
				indicator.style.right = "0";
				containerRef.current.appendChild(indicator);
				resizeIndicatorRef.current = indicator;
			}

			// Position the indicator at the exact mouse location on mousedown
			if (resizeIndicatorRef.current && containerRef.current) {
				const containerRect = containerRef.current.getBoundingClientRect();
				resizeIndicatorRef.current.style.top = `${e.clientY - containerRect.top}px`;
			}
		}
		startPosRef.current = { x: e.clientX, y: e.clientY };
	};

	useEffect(() => {
		if (tableRef.current) {
			tableWidthRef.current = tableRef.current.offsetWidth;
		}
		// Always sync state with props when they change
		// Use provided defaults if not present (15mm width, 10mm height)
		const initialColWidths =
			tableProperties.columnWidths || Array(tableProperties.columns).fill(15);
		const initialRowHeights =
			tableProperties.rowHeights || Array(tableProperties.rows).fill(10);

		// Use shared helper to compute scaled dimensions based on available container size
		const { scaledWidths, scaledHeights } = calculateScaledDimensions(
			initialColWidths,
			initialRowHeights,
			containerWidthMm,
			containerHeightMm,
		);

		setColumnWidths(scaledWidths);
		setRowHeights(scaledHeights);
		setIntrinsicColumnWidths(initialColWidths);
		setIntrinsicRowHeights(initialRowHeights);
	}, [
		tableProperties.columnWidths,
		tableProperties.rowHeights,
		tableProperties.columns,
		tableProperties.rows,
		containerWidthMm,
		containerHeightMm,
	]); // Update when dimensions change

	// Effect to apply selectedCellsBackgroundColor
	useEffect(() => {
		if (tableProperties.selectedCellsBackgroundColor && selectedCells) {
			const newCells = tableProperties.cells.map((row, rIdx) =>
				row.map((cell, cIdx) => {
					const isCellSelected =
						rIdx >= Math.min(selectedCells.start.row, selectedCells.end.row) &&
						rIdx <= Math.max(selectedCells.start.row, selectedCells.end.row) &&
						cIdx >= Math.min(selectedCells.start.col, selectedCells.end.col) &&
						cIdx <= Math.max(selectedCells.start.col, selectedCells.end.col);

					if (isCellSelected) {
						return {
							...cell,
							backgroundColor:
								tableProperties.selectedCellsBackgroundColor || null,
						};
					}
					return cell;
				}),
			);

			onChange({
				...tableProperties,
				cells: newCells,
				selectedCellsBackgroundColor: undefined, // Clear the color after applying
			});
		}
	}, [tableProperties, selectedCells, onChange]);

	// This useEffect is responsible for calling onChange when selectedCells (internal state) changes
	// and is different from what is currently in tableProperties.selection.
	useEffect(() => {
		const currentReportedSelection = tableProperties.selection;

		if (!areSelectionsEqual(selectedCells, currentReportedSelection)) {
			onChange({
				...tableProperties,
				selection: selectedCells,
			});
		}
	}, [selectedCells, onChange, tableProperties]); // tableProperties is a dep because we spread it and read .selection

	useEffect(() => {
		// Capture the table ref value to use in cleanup
		const table = tableRef.current;

		const handleMouseMove = (e: MouseEvent) => {
			if (
				resizingCol !== null &&
				tableRef.current &&
				resizeIndicatorRef.current
			) {
				// Directly position the guide at the cursor (we'll enforce min-width later on mouseup)
				const containerRect = containerRef.current?.getBoundingClientRect();
				if (containerRect) {
					const indicatorPos = e.clientX - containerRect.left;
					resizeIndicatorRef.current.style.left = `${indicatorPos}px`;
				}
			}
			if (
				resizingRow !== null &&
				tableRef.current &&
				resizeIndicatorRef.current
			) {
				// Directly position the guide at the cursor (we'll enforce min-height later on mouseup)
				const containerRect = containerRef.current?.getBoundingClientRect();
				if (containerRect) {
					const indicatorPos = e.clientY - containerRect.top;
					resizeIndicatorRef.current.style.top = `${indicatorPos}px`;
				}
			}
		};

		const handleMouseUp = (e: MouseEvent) => {
			if ((resizingCol !== null || resizingRow !== null) && tableRef.current) {
				// Remove highlight from all cells
				const cells = tableRef.current.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.remove("bg-blue-50");
				}

				// Calculate final sizes and update state
				if (resizingCol !== null) {
					const diffPx = e.clientX - startPosRef.current.x;
					const diffMm = pxToMm(diffPx);
					const initialWidth = initialSizeRef.current.width || 15;
					const newCurrentWidthMm = roundToTwoDecimals(
						Math.max(minWidthMm, initialWidth + diffMm),
					);

					// Update intrinsic (unscaled) dimensions in tableProperties
					const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
					newIntrinsicColumnWidths[resizingCol] = newCurrentWidthMm;

					onChange({
						...tableProperties,
						columnWidths: newIntrinsicColumnWidths,
						rowHeights: intrinsicRowHeights,
					});
				}

				if (resizingRow !== null) {
					const diffPx = e.clientY - startPosRef.current.y;
					const diffMm = pxToMm(diffPx);
					const initialHeight = initialSizeRef.current.height || 10;
					const newHeightMm = roundToTwoDecimals(
						Math.max(minHeightMm, initialHeight + diffMm),
					);

					// Update intrinsic (unscaled) dimensions in tableProperties
					const newIntrinsicRowHeights = [...intrinsicRowHeights];
					newIntrinsicRowHeights[resizingRow] = newHeightMm;

					onChange({
						...tableProperties,
						columnWidths: intrinsicColumnWidths,
						rowHeights: newIntrinsicRowHeights,
					});
				}

				// Remove resize indicator
				if (resizeIndicatorRef.current) {
					resizeIndicatorRef.current.remove();
					resizeIndicatorRef.current = null;
				}
			}
			setResizingCol(null);
			setResizingRow(null);
			setIsResizing(false);
			initialSizeRef.current = {};
		};

		if (resizingCol !== null || resizingRow !== null) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);
		}

		return () => {
			// Remove highlights when unmounting or cleaning up
			if (table) {
				const cells = table.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.remove("bg-blue-50");
				}
			}
			document.removeEventListener("mousemove", handleMouseMove);
			document.removeEventListener("mouseup", handleMouseUp);
		};
	}, [
		resizingCol,
		resizingRow,
		intrinsicColumnWidths,
		intrinsicRowHeights,
		onChange,
		tableProperties,
	]);

	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key === "Shift") setIsShiftPressed(true);
		};

		const handleKeyUp = (e: KeyboardEvent) => {
			if (e.key === "Shift") setIsShiftPressed(false);
		};

		window.addEventListener("keydown", handleKeyDown);
		window.addEventListener("keyup", handleKeyUp);

		return () => {
			window.removeEventListener("keydown", handleKeyDown);
			window.removeEventListener("keyup", handleKeyUp);
		};
	}, []);

	const handleCellSelection = (rowIndex: number, colIndex: number) => {
		// Convert cell array indices to logical grid position
		const { logicalRow, logicalCol } = cellToLogicalPosition(
			tableProperties.cells,
			rowIndex,
			colIndex,
		);

		let rawSelection: {
			start: { row: number; col: number };
			end: { row: number; col: number };
		};
		if (!isShiftPressed || !selectedCells) {
			// Single cell selection - use logical coordinates
			rawSelection = {
				start: { row: logicalRow, col: logicalCol },
				end: { row: logicalRow, col: logicalCol },
			};
		} else {
			// Extend selection when shift is pressed - use logical coordinates
			rawSelection = {
				start: selectedCells.start,
				end: { row: logicalRow, col: logicalCol },
			};
		}

		// Always expand selection to handle merged cells properly
		const expandedSelection = expandSelectionForMergedCells(
			tableProperties.cells,
			rawSelection,
			tableProperties.rows,
			tableProperties.columns,
		);

		setSelectedCells(expandedSelection);
	};

	const handleMouseDown = (
		e: React.MouseEvent,
		rowIndex: number,
		colIndex: number,
	) => {
		if (e.detail === 2) {
			handleDoubleClick(e, rowIndex, colIndex);
			return;
		}

		if (isResizing) return; // Don't start selection if we're resizing

		// If editing this cell, don't prevent default to allow text cursor positioning
		if (editingCell?.row === rowIndex && editingCell?.col === colIndex) {
			return;
		}

		// Prevent default text selection behavior
		e.preventDefault();

		// Clear editing cell if clicking a different cell
		if (
			editingCell &&
			(editingCell.row !== rowIndex || editingCell.col !== colIndex)
		) {
			setEditingCell(null);
		}

		setIsDragging(true);
		handleCellSelection(rowIndex, colIndex);
	};

	const handleMouseEnter = (rowIndex: number, colIndex: number) => {
		if (isDragging && selectedCells) {
			// Convert cell array indices to logical grid position
			const { logicalRow, logicalCol } = cellToLogicalPosition(
				tableProperties.cells,
				rowIndex,
				colIndex,
			);
			const rawSelection = {
				start: selectedCells.start,
				end: { row: logicalRow, col: logicalCol },
			};

			// Expand selection to include full merged cells
			const expandedSelection = expandSelectionForMergedCells(
				tableProperties.cells,
				rawSelection,
				tableProperties.rows,
				tableProperties.columns,
			);

			setSelectedCells(expandedSelection);

			// Update logical cursor position to the clicked cell's logical position
			setLogicalCursorPosition({ row: logicalRow, col: logicalCol });
		}
	};

	const handleMouseUp = useCallback(() => {
		setIsDragging(false);
	}, []);

	useEffect(() => {
		if (isDragging) {
			window.addEventListener("mouseup", handleMouseUp);
			return () => window.removeEventListener("mouseup", handleMouseUp);
		}
	}, [isDragging, handleMouseUp]);

	const isSingleCellSelected =
		selectedCells &&
		selectedCells.start.row === selectedCells.end.row &&
		selectedCells.start.col === selectedCells.end.col;

	const addColumn = () => {
		if (!tableProperties.cells || tableProperties.cells.length === 0) return;

		const targetIndex = selectedCells
			? selectedCells.end.col
			: tableProperties.columns - 1; // logical index

		const defaultCell = {
			content: "",
			colspan: 1,
			rowspan: 1,
			borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			backgroundColor: "transparent",
			verticalAlign: "top" as const,
		} as TableCell;

		const updatedCells = tableProperties.cells.map((row) => {
			let colCursor = 0;
			let inserted = false;
			const newRow: TableCell[] = [];

			for (const cell of row) {
				const start = colCursor;
				const end = colCursor + cell.colspan - 1;

				if (!inserted && targetIndex < start) {
					// Insert before current block (should not really happen)
					newRow.push({ ...defaultCell });
					inserted = true;
				}

				if (targetIndex >= start && targetIndex <= end) {
					// Insertion point lies within this cell's span
					newRow.push({ ...cell, colspan: cell.colspan + 1 });
					inserted = true;
				} else {
					newRow.push(cell);
					// If insertion is right after this cell's end, place new cell
					if (!inserted && end === targetIndex) {
						newRow.push({ ...defaultCell });
						inserted = true;
					}
				}
				colCursor += cell.colspan;
			}

			if (!inserted) {
				newRow.push({ ...defaultCell }); // append at the end
			}

			return newRow;
		});

		// Update intrinsic column widths (15mm default for new column)
		const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
		newIntrinsicColumnWidths.splice(targetIndex + 1, 0, 15);

		onChange({
			...tableProperties,
			cells: updatedCells,
			columnWidths: newIntrinsicColumnWidths,
			columns: tableProperties.columns + 1,
		});
	};

	const addRow = () => {
		if (!tableProperties.cells || tableProperties.cells.length === 0) return;

		const targetIndex = selectedCells
			? selectedCells.end.row
			: tableProperties.rows - 1;

		const newRowIdx = targetIndex + 1;
		const columns = tableProperties.columns;

		const defaultCell = {
			content: "",
			colspan: 1,
			rowspan: 1,
			borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			backgroundColor: "transparent",
			verticalAlign: "top" as const,
		} as TableCell;

		// Deep copy cells
		const newCells = JSON.parse(
			JSON.stringify(tableProperties.cells),
		) as TableCell[][];

		// Track columns covered by existing rowspans that reach newRowIdx
		const coveredColumns = new Set<number>();

		for (let r = 0; r < newRowIdx; r++) {
			let colCursor = 0;
			for (const cell of newCells[r]) {
				if (r + cell.rowspan - 1 >= newRowIdx) {
					for (let c = colCursor; c < colCursor + cell.colspan; c++) {
						coveredColumns.add(c);
					}
					cell.rowspan += 1; // extend the cell downward
				}
				colCursor += cell.colspan;
			}
		}

		// Build the new row taking into account covered columns
		const newRow: TableCell[] = [];
		for (let c = 0; c < columns; c++) {
			if (coveredColumns.has(c)) continue; // no cell because covered by rowspan
			newRow.push({ ...defaultCell });
		}

		newCells.splice(newRowIdx, 0, newRow);

		const newIntrinsicRowHeights = [...intrinsicRowHeights];
		newIntrinsicRowHeights.splice(newRowIdx, 0, 10); // default 10mm height

		onChange({
			...tableProperties,
			cells: newCells,
			rowHeights: newIntrinsicRowHeights,
			rows: tableProperties.rows + 1,
		});
	};

	const deleteColumn = () => {
		if (!isSingleCellSelected || !tableProperties.cells) return;
		if (tableProperties.columns <= 1) return;
		const targetIndex = selectedCells?.end.col;
		if (targetIndex === undefined) return;

		const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
		newIntrinsicColumnWidths.splice(targetIndex, 1);

		const updatedCells = tableProperties.cells.map((row) => {
			let colCursor = 0;
			const newRow: TableCell[] = [];
			for (const cell of row) {
				const start = colCursor;
				const end = colCursor + cell.colspan - 1;

				if (targetIndex >= start && targetIndex <= end) {
					// Column falls within this cell's span
					if (cell.colspan > 1) {
						newRow.push({ ...cell, colspan: cell.colspan - 1 });
					}
					// If colspan ==1, effectively remove the cell
				} else {
					newRow.push(cell);
				}
				colCursor += cell.colspan;
			}
			return newRow;
		});

		onChange({
			...tableProperties,
			cells: updatedCells,
			columnWidths: newIntrinsicColumnWidths,
			columns: tableProperties.columns - 1,
		});

		setSelectedCells(null);
		setLogicalCursorPosition(null);
	};

	const deleteRow = () => {
		if (!isSingleCellSelected || !tableProperties.cells) return;
		if (tableProperties.rows <= 1) return;
		const targetRowIdx = selectedCells?.end.row;
		if (targetRowIdx === undefined) return;

		const newCells = JSON.parse(
			JSON.stringify(tableProperties.cells),
		) as TableCell[][];

		// Adjust rowspan for cells above the deleted row
		for (let r = 0; r < targetRowIdx; r++) {
			for (const cell of newCells[r]) {
				if (r + cell.rowspan - 1 >= targetRowIdx) {
					cell.rowspan -= 1;
				}
			}
		}

		// Remove the row itself
		newCells.splice(targetRowIdx, 1);

		// Build new row heights
		const newIntrinsicRowHeights = [...intrinsicRowHeights];
		newIntrinsicRowHeights.splice(targetRowIdx, 1);

		onChange({
			...tableProperties,
			cells: newCells,
			rowHeights: newIntrinsicRowHeights,
			rows: tableProperties.rows - 1,
		});

		setSelectedCells(null);
		setLogicalCursorPosition(null);
	};

	const handleDoubleClick = (
		e: React.MouseEvent,
		rowIndex: number,
		colIndex: number,
	) => {
		e.stopPropagation();
		if (isResizing) return;

		// Clear any existing editing cell before setting new one
		if (
			editingCell &&
			(editingCell.row !== rowIndex || editingCell.col !== colIndex)
		) {
			setEditingCell(null);
		}

		setEditingCell({ row: rowIndex, col: colIndex });

		// Focus the RichTextEditor directly
		// If we start editing, treat it as a single cell selection for properties tab
		// Convert to logical coordinates for consistency
		const { logicalRow, logicalCol } = cellToLogicalPosition(
			tableProperties.cells,
			rowIndex,
			colIndex,
		);
		const rawSelection = {
			start: { row: logicalRow, col: logicalCol },
			end: { row: logicalRow, col: logicalCol },
		};

		// Expand selection to handle merged cells properly
		const expandedSelection = expandSelectionForMergedCells(
			tableProperties.cells,
			rawSelection,
			tableProperties.rows,
			tableProperties.columns,
		);

		setSelectedCells(expandedSelection);
		// onChange will be triggered by useEffect listening to selectedCells

		setTimeout(() => {
			const editorElement = document.querySelector(".ProseMirror");
			if (editorElement) {
				(editorElement as HTMLElement).focus();
			}
		}, 0);
	};

	const handleCellContentChange = (content: string) => {
		if (!editingCell) return;

		const newCells = [...tableProperties.cells];

		// Extract vertical alignment from content if present
		let verticalAlign: "top" | "middle" | "bottom" | undefined;
		const styleMatch = content.match(/vertical-align:\s*(top|middle|bottom)/);
		const classMatch = content.match(/vertical-align-(top|middle|bottom)/);

		if (styleMatch) {
			verticalAlign = styleMatch[1] as "top" | "middle" | "bottom";
		} else if (classMatch) {
			verticalAlign = classMatch[1] as "top" | "middle" | "bottom";
		}

		newCells[editingCell.row][editingCell.col] = {
			...newCells[editingCell.row][editingCell.col],
			content,
			verticalAlign:
				verticalAlign ||
				newCells[editingCell.row][editingCell.col].verticalAlign ||
				"top",
		};

		onChange({
			...tableProperties,
			cells: newCells,
		});
	};

	// Define the cell rendering function for edit mode
	const renderEditableCell = (
		cell: TableCell,
		rowIndex: number,
		colIndex: number,
	): React.ReactNode => {
		const isSelected =
			selectedCells &&
			isCellSelectedInLogicalGrid(
				tableProperties.cells,
				rowIndex,
				colIndex,
				selectedCells,
			);

		const isEditing =
			editingCell?.row === rowIndex && editingCell?.col === colIndex;

		// Determine background color:
		const cellOwnBackgroundColor = cell.backgroundColor
			? cell.backgroundColor
			: "transparent";

		// Compute logical column start index (accounts for previous colspans)
		const logicalColStart = tableProperties.cells[rowIndex]
			.slice(0, colIndex)
			.reduce((sum, c) => sum + c.colspan, 0);
		const spannedWidthMm = columnWidths
			.slice(logicalColStart, logicalColStart + cell.colspan)
			.reduce((sum, w) => sum + (w || 0), 0);
		const cellWidthStyle =
			spannedWidthMm > 0 ? `${spannedWidthMm}mm` : undefined;

		// Border styling
		const borderStyle = tableProperties.borderStyle || "solid";
		const globalTableBorderColor = tableProperties.borderColor || "black";

		// Top Border
		const topWidth = cell.borderSettings?.top?.width ?? cell.borderWidths.top;
		const topColor = cell.borderSettings?.top?.color || globalTableBorderColor;
		const borderTop = `${topWidth}px ${borderStyle} ${topColor}`;

		// Right Border
		const rightWidth =
			cell.borderSettings?.right?.width ?? cell.borderWidths.right;
		const rightColor =
			cell.borderSettings?.right?.color || globalTableBorderColor;
		const borderRight = `${rightWidth}px ${borderStyle} ${rightColor}`;

		// Bottom Border
		const bottomWidth =
			cell.borderSettings?.bottom?.width ?? cell.borderWidths.bottom;
		const bottomColor =
			cell.borderSettings?.bottom?.color || globalTableBorderColor;
		const borderBottom = `${bottomWidth}px ${borderStyle} ${bottomColor}`;

		// Left Border
		const leftWidth =
			cell.borderSettings?.left?.width ?? cell.borderWidths.left;
		const leftColor =
			cell.borderSettings?.left?.color || globalTableBorderColor;
		const borderLeft = `${leftWidth}px ${borderStyle} ${leftColor}`;

		return (
			<td
				key={`${rowIndex}-${colIndex}`}
				colSpan={cell.colspan}
				rowSpan={cell.rowspan}
				onMouseDown={(e) => {
					// Don't handle mousedown if we're editing this cell
					if (isEditing) {
						return;
					}
					handleMouseDown(e, rowIndex, colIndex);
				}}
				onMouseEnter={() => {
					// Don't handle mouse enter if we're editing
					if (editingCell) {
						return;
					}
					handleMouseEnter(rowIndex, colIndex);
				}}
				style={{
					// Replace the single 'border' style with individual border styles
					borderTop: borderTop,
					borderRight: borderRight,
					borderBottom: borderBottom,
					borderLeft: borderLeft,
					padding: `2px`,
					backgroundColor: cellOwnBackgroundColor, // Apply cell's own background color
					position: "relative",
					minWidth: "5px",
					width: cellWidthStyle || `15mm`, // Sum of spanned column widths
					// Height for cells spanning multiple rows
					height: (() => {
						const h = rowHeights
							.slice(rowIndex, rowIndex + cell.rowspan)
							.reduce((sum, v) => sum + (v || 0), 0);
						return `${h || 10}mm`;
					})(),
					// Max height for cells spanning multiple rows
					maxHeight: (() => {
						const h = rowHeights
							.slice(rowIndex, rowIndex + cell.rowspan)
							.reduce((sum, v) => sum + (v || 0), 0);
						return `${h || 10}mm`;
					})(),
					boxSizing: "border-box",
					wordBreak: "break-word",
					overflowWrap: "break-word",
					whiteSpace: "pre-wrap",
					verticalAlign: cell.verticalAlign || "top",
					overflow: "hidden",
				}}
			>
				{/* Wrapper for content to ensure it's above the selection overlay */}
				<div
					style={{
						position: "relative",
						zIndex: 2,
						height: "100%",
						maxHeight: "100%",
						overflow: "hidden",
						userSelect: isEditing ? "text" : isDragging ? "none" : "auto",
						WebkitUserSelect: isEditing ? "text" : isDragging ? "none" : "auto",
						MozUserSelect: isEditing ? "text" : isDragging ? "none" : "auto",
						msUserSelect: isEditing ? "text" : isDragging ? "none" : undefined,
						pointerEvents: isEditing ? "auto" : "none", // Allow clicks to pass through when not editing
						display: "flex",
						flexDirection: "column",
						justifyContent:
							cell.verticalAlign === "middle"
								? "center"
								: cell.verticalAlign === "bottom"
									? "flex-end"
									: "flex-start",
					}}
				>
					{isEditing ? (
						<div
							style={{
								height: "100%",
								maxHeight: "100%",
								overflow: "hidden",
								position: "relative",
								zIndex: 3, // Ensure editor is above other elements
							}}
						>
							<RichTextEditor
								content={cell.content}
								onChange={handleCellContentChange}
								setActiveEditor={setActiveEditor}
								verticalAlign={cell.verticalAlign || "top"}
								setIsTextEditorFocused={setIsTextEditorFocused}
							/>
						</div>
					) : (
						<div
							className="max-w-none w-full"
							style={{
								fontFamily: "'NeoSansforeprimo-Regular', sans-serif",
								whiteSpace: "pre-wrap",
								wordBreak: "break-word",
								overflowWrap: "break-word",
								overflow: "hidden",
								textOverflow: "ellipsis",
								lineHeight: "1.2",
								maxHeight: "100%",
								pointerEvents: "none", // Always allow clicks to pass through to the cell
								height: "100%",
								display: "flex",
								flexDirection: "column",
								justifyContent:
									cell.verticalAlign === "middle"
										? "center"
										: cell.verticalAlign === "bottom"
											? "flex-end"
											: "flex-start",
							}}
							data-element-content
						>
							<div
								// biome-ignore lint/security/noDangerouslySetInnerHtml: is needed
								dangerouslySetInnerHTML={{
									__html: processContentWithVariables({
										content: cell.content || "",
										isEditing: false,
										highlightVariables, // Disable highlighting when cell is selected
										selectedTestDataIndex,
										testData,
									}),
								}}
							/>
						</div>
					)}
				</div>

				{/* Selection Overlay - hide when editing */}
				{isSelected && !isEditing && (
					<div
						style={{
							position: "absolute",
							top: 0,
							left: 0,
							right: 0,
							bottom: 0,
							backgroundColor: "rgba(0, 120, 212, 0.3)", // Selection color with alpha
							zIndex: 1, // Below content, above cell background
							pointerEvents: "none", // Allow clicks to pass through
						}}
					/>
				)}

				{!isDragging && !isEditing && (
					<div
						className="absolute top-0 right-0 w-1 cursor-col-resize"
						role="button"
						tabIndex={0}
						onMouseDown={(e) => handleResizeStart(e, colIndex, "col")}
						style={{
							zIndex: 2,
							height: `${tableRef.current?.offsetHeight || 0}px`,
							background: "transparent",
						}}
					/>
				)}
				{!isDragging && !isEditing && (
					<div
						className="absolute bottom-0 left-0 h-1 cursor-row-resize"
						role="button"
						tabIndex={0}
						onMouseDown={(e) => handleResizeStart(e, rowIndex, "row")}
						style={{
							zIndex: 2,
							width: `${tableRef.current?.offsetWidth || 0}px`,
							background: "transparent",
						}}
					/>
				)}
			</td>
		);
	};

	const renderAddButtons = () => {
		if (isSingleCellSelected && tableRef.current) {
			// Find the actual cell element by iterating through rows
			let targetCell: HTMLTableCellElement | null = null;
			const rows = tableRef.current.getElementsByTagName("tr");
			if (rows[selectedCells?.end.row]) {
				const cells = rows[selectedCells?.end.row].getElementsByTagName("td");
				// We need to find which actual cell index corresponds to our logical column
				let logicalCol = 0;

				for (let i = 0; i < cells.length; i++) {
					if (logicalCol === selectedCells?.end.col) {
						targetCell = cells[i];
						break;
					}
					const colspan = parseInt(cells[i].getAttribute("colspan") || "1");
					logicalCol += colspan;
				}
			}

			if (!targetCell) return null;

			return (
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="absolute hover:bg-slate-100 z-10"
								style={{
									top: `${targetCell.offsetTop}px`,
									left: `${targetCell.offsetLeft + targetCell.offsetWidth}px`,
								}}
								onClick={(e) => {
									e.preventDefault();
									e.stopPropagation();
									addColumn();
								}}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Spalte hinzufügen</TooltipContent>
					</Tooltip>

					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="absolute hover:bg-slate-100 z-10"
								style={{
									top: `${targetCell.offsetTop + targetCell.offsetHeight}px`,
									left: `${targetCell.offsetLeft}px`,
								}}
								onClick={(e) => {
									e.preventDefault();
									e.stopPropagation();
									addRow();
								}}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Zeile hinzufügen</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			);
		}

		// Show buttons at the end when no cell is selected
		const lastRowIndex = tableProperties.cells.length - 1;
		const lastColIndex = tableProperties.cells[0].length - 1;

		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-slate-100"
							style={{
								top: `${tableRef.current?.rows[0]?.offsetTop || 0}px`,
								left: `${
									(tableRef.current?.rows[0]?.cells[lastColIndex]?.offsetLeft ||
										0) +
									(tableRef.current?.rows[0]?.cells[lastColIndex]
										?.offsetWidth || 0)
								}px`,
							}}
							onClick={addColumn}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Spalte hinzufügen</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-slate-100"
							style={{
								top: `${
									(tableRef.current?.rows[lastRowIndex]?.offsetTop || 0) +
									(tableRef.current?.rows[lastRowIndex]?.offsetHeight || 0)
								}px`,
								left: "0px",
							}}
							onClick={addRow}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zeile hinzufügen</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	};

	const highlightRow = (rowIndex: number, highlight: boolean) => {
		if (!tableRef.current) return;
		const row = tableRef.current.getElementsByTagName("tr")[rowIndex];
		if (row) {
			const cells = row.getElementsByTagName("td");
			for (let i = 0; i < cells.length; i++) {
				if (highlight) {
					// Store original background in a data attribute
					cells[i].setAttribute(
						"data-original-bg",
						cells[i].style.backgroundColor || "",
					);
					// Apply red overlay using a semi-transparent div
					const overlay = document.createElement("div");
					overlay.className = "delete-highlight-overlay";
					cells[i].style.position = "relative";
					cells[i].appendChild(overlay);
				} else {
					// Remove overlay
					const overlay = cells[i].querySelector(".delete-highlight-overlay");
					if (overlay) {
						overlay.remove();
					}
				}
			}
		}
	};

	const highlightColumn = (colIndex: number, highlight: boolean) => {
		if (!tableRef.current) return;
		const rows = tableRef.current.getElementsByTagName("tr");
		for (let i = 0; i < rows.length; i++) {
			const cells = rows[i].getElementsByTagName("td");
			let logicalCol = 0;
			for (let j = 0; j < cells.length; j++) {
				if (logicalCol === colIndex) {
					if (highlight) {
						// Store original background in a data attribute
						cells[j].setAttribute(
							"data-original-bg",
							cells[j].style.backgroundColor || "",
						);
						// Apply red overlay using a semi-transparent div
						const overlay = document.createElement("div");
						overlay.className = "delete-highlight-overlay";
						cells[j].style.position = "relative";
						cells[j].appendChild(overlay);
					} else {
						// Remove overlay
						const overlay = cells[j].querySelector(".delete-highlight-overlay");
						if (overlay) {
							overlay.remove();
						}
					}
					break;
				}
				const colspan = parseInt(cells[j].getAttribute("colspan") || "1");
				logicalCol += colspan;
			}
		}
	};

	const renderDeleteButtons = () => {
		if (!isSingleCellSelected || !tableRef.current) return null;

		// Find the actual cell element
		let targetCell: HTMLTableCellElement | null = null;
		const rows = tableRef.current.getElementsByTagName("tr");
		if (rows[selectedCells?.end.row]) {
			const cells = rows[selectedCells?.end.row].getElementsByTagName("td");
			let logicalCol = 0;

			for (let i = 0; i < cells.length; i++) {
				if (logicalCol === selectedCells?.end.col) {
					targetCell = cells[i];
					break;
				}
				const colspan = parseInt(cells[i].getAttribute("colspan") || "1");
				logicalCol += colspan;
			}
		}

		if (!targetCell) return null;

		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-red-100 z-20"
							style={{
								top: `${targetCell.offsetTop}px`,
								left: `-45px`, // Position to the left of the table
							}}
							onMouseEnter={() => highlightRow(selectedCells?.end.row, true)}
							onMouseLeave={() => highlightRow(selectedCells?.end.row, false)}
							onMouseDown={(e) => {
								console.log("Delete row button clicked");
								e.preventDefault();
								e.stopPropagation();
							}}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								deleteRow();
							}}
						>
							<Trash2 className="h-4 w-4 text-red-500" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zeile löschen</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-red-100 z-20"
							style={{
								top: `-45px`, // Position above the table
								left: `${targetCell.offsetLeft}px`,
							}}
							onMouseEnter={() => highlightColumn(selectedCells?.end.col, true)}
							onMouseLeave={() =>
								highlightColumn(selectedCells?.end.col, false)
							}
							onMouseDown={(e) => {
								console.log("Delete column button clicked");
								e.preventDefault();
								e.stopPropagation();
							}}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								deleteColumn();
							}}
						>
							<Trash2 className="h-4 w-4 text-red-500" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Spalte löschen</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	};

	// Clean up highlights when selection changes
	useEffect(() => {
		if (!selectedCells && tableRef.current) {
			// Clear all red highlights when no selection
			const cells = tableRef.current.getElementsByTagName("td");
			for (let i = 0; i < cells.length; i++) {
				cells[i].classList.remove("bg-red-50");
				// Remove any delete highlight overlays
				const overlay = cells[i].querySelector(".delete-highlight-overlay");
				if (overlay) {
					overlay.remove();
				}
			}
		}
	}, [selectedCells]);

	useEffect(() => {
		const handleClickOutsideEvent = (e: MouseEvent) => {
			const target = e.target as HTMLElement;

			// Check if click is within container OR on a delete button (which might be outside due to absolute positioning)
			const isClickOnTableEditor =
				containerRef.current?.contains(target) ||
				target.closest("button")?.closest('[style*="absolute"]');

			// Check for interactions with UI elements that should not cause deselection.
			// This includes the format toolbar itself, and Radix-based popovers/dialogs
			// (used by ShadCN/UI) which are often portalled.
			const isToolbarOrPopupInteraction =
				!!target.closest(".format-toolbar") || // Sidebar toolbar itself
				!!target.closest("[data-radix-popper-content-wrapper]") || // General Radix popper content (covers Popover, Tooltip, Select)
				!!target.closest('[role="dialog"]'); // General dialogs, often used by Radix for modal content

			// If currently editing a cell
			if (editingCell) {
				const editorWasClicked =
					isClickOnTableEditor && target.closest(".ProseMirror");
				if (!editorWasClicked && !isToolbarOrPopupInteraction) {
					setEditingCell(null); // Stop editing if click is outside editor and not on a related popup/toolbar
				}
			}

			// If click is NOT on the table editor AND NOT on the toolbar/popup, then clear selection.
			if (!isClickOnTableEditor && !isToolbarOrPopupInteraction) {
				if (selectedCells) {
					// Only clear selection if one exists
					setSelectedCells(null);
					setLogicalCursorPosition(null);
				}
			}
			// Optional: Handle clicks inside the table editor's div but not on a cell or known interactive element.
			// else if (isClickOnTableEditor &&
			//          !target.closest('td') &&
			//          !target.closest('button') && // Catches add/delete buttons
			//          !target.closest('[class*="resize"]')) { // Catches resize handles
			//     // This means a click on the table's padding area for example.
			//     // Clearing selection here might be too aggressive, depending on desired UX.
			//     // For now, we only ensure editing stops (handled above).
			// }
		};

		document.addEventListener("mousedown", handleClickOutsideEvent);
		return () =>
			document.removeEventListener("mousedown", handleClickOutsideEvent);
	}, [editingCell, selectedCells]); // Dependencies are correct

	// --- Keyboard navigation helpers ---
	// Focus the container whenever we enter the editor so that key events are captured
	useEffect(() => {
		if (containerRef.current) {
			containerRef.current.tabIndex = 0;
			// Auto-focus when editor mounts (but only once)
			containerRef.current.focus();
		}
	}, []);

	// Handle key navigation, editing and escape
	useEffect(() => {
		// Helper function to find the next navigable cell in a given direction
		function findNextNavigableCell(
			currentRow: number,
			currentCol: number,
			direction: "up" | "down" | "left" | "right",
		): { row: number; col: number } | null {
			const maxRow = tableProperties.rows - 1;
			const maxCol = tableProperties.columns - 1;

			// Create logical grid to understand cell positions
			const grid = createLogicalGrid(
				tableProperties.cells,
				tableProperties.rows,
				tableProperties.columns,
			);

			// Get all unique cell positions (start positions of actual cells)
			const cellPositions: Array<{ row: number; col: number }> = [];
			for (
				let rowIndex = 0;
				rowIndex < tableProperties.cells.length;
				rowIndex++
			) {
				const row = tableProperties.cells[rowIndex];
				let logicalCol = 0;
				for (let colIndex = 0; colIndex < row.length; colIndex++) {
					const cell = row[colIndex];
					// Find the logical position where this cell starts
					while (
						logicalCol < tableProperties.columns &&
						grid[rowIndex] &&
						grid[rowIndex][logicalCol] &&
						(grid[rowIndex][logicalCol]?.rowIndex !== rowIndex ||
							grid[rowIndex][logicalCol]?.colIndex !== colIndex)
					) {
						logicalCol++;
					}
					if (logicalCol < tableProperties.columns) {
						cellPositions.push({ row: rowIndex, col: logicalCol });
						logicalCol += cell.colspan || 1;
					}
				}
			}

			// Sort positions for easier navigation
			cellPositions.sort((a, b) => {
				if (a.row !== b.row) return a.row - b.row;
				return a.col - b.col;
			});

			// Find current position in the sorted list
			const currentIndex = cellPositions.findIndex(
				(pos) => pos.row === currentRow && pos.col === currentCol,
			);

			if (currentIndex === -1) {
				// Current position not found, return first available position
				return cellPositions.length > 0 ? cellPositions[0] : null;
			}

			switch (direction) {
				case "up": {
					// Find the cell in the previous row that's closest to current column
					const targetRow = currentRow - 1;
					if (targetRow < 0) return null;

					const candidatesInTargetRow = cellPositions.filter(
						(pos) => pos.row === targetRow,
					);
					if (candidatesInTargetRow.length === 0) {
						// No cells in target row, try recursively
						return findNextNavigableCell(targetRow, currentCol, direction);
					}

					// Find closest cell by column
					let closest = candidatesInTargetRow[0];
					for (const candidate of candidatesInTargetRow) {
						if (
							Math.abs(candidate.col - currentCol) <
							Math.abs(closest.col - currentCol)
						) {
							closest = candidate;
						}
					}
					return closest;
				}

				case "down": {
					// Find the cell in the next row that's closest to current column
					const targetRow = currentRow + 1;
					if (targetRow > maxRow) return null;

					const candidatesInTargetRow = cellPositions.filter(
						(pos) => pos.row === targetRow,
					);
					if (candidatesInTargetRow.length === 0) {
						// No cells in target row, try recursively
						return findNextNavigableCell(targetRow, currentCol, direction);
					}

					// Find closest cell by column
					let closest = candidatesInTargetRow[0];
					for (const candidate of candidatesInTargetRow) {
						if (
							Math.abs(candidate.col - currentCol) <
							Math.abs(closest.col - currentCol)
						) {
							closest = candidate;
						}
					}
					return closest;
				}

				case "left": {
					// Find previous cell in same row, or last cell in previous row
					const candidatesInCurrentRow = cellPositions.filter(
						(pos) => pos.row === currentRow && pos.col < currentCol,
					);

					if (candidatesInCurrentRow.length > 0) {
						// Return rightmost cell in current row that's to the left
						return candidatesInCurrentRow[candidatesInCurrentRow.length - 1];
					}

					// No cells to the left in current row, go to previous row
					const targetRow = currentRow - 1;
					if (targetRow < 0) return null;

					const candidatesInTargetRow = cellPositions.filter(
						(pos) => pos.row === targetRow,
					);
					if (candidatesInTargetRow.length === 0) {
						// No cells in target row, try recursively
						return findNextNavigableCell(targetRow, maxCol, direction);
					}

					// Return rightmost cell in target row
					return candidatesInTargetRow[candidatesInTargetRow.length - 1];
				}

				case "right": {
					// Find next cell in same row, or first cell in next row
					const candidatesInCurrentRow = cellPositions.filter(
						(pos) => pos.row === currentRow && pos.col > currentCol,
					);

					if (candidatesInCurrentRow.length > 0) {
						// Return leftmost cell in current row that's to the right
						return candidatesInCurrentRow[0];
					}

					// No cells to the right in current row, go to next row
					const targetRow = currentRow + 1;
					if (targetRow > maxRow) return null;

					const candidatesInTargetRow = cellPositions.filter(
						(pos) => pos.row === targetRow,
					);
					if (candidatesInTargetRow.length === 0) {
						// No cells in target row, try recursively
						return findNextNavigableCell(targetRow, 0, direction);
					}

					// Return leftmost cell in target row
					return candidatesInTargetRow[0];
				}

				default:
					return null;
			}
		}

		function handleKeyboardNavigation(e: KeyboardEvent) {
			// When a rich-text editor inside a cell is active we only react to Escape
			if (editingCell) {
				if (e.key === "Escape") {
					e.preventDefault();
					setEditingCell(null);
					// After leaving edit mode, refocus the container so that further
					// navigation keys are caught again
					setTimeout(() => {
						containerRef.current?.focus();
					}, 0);
				}
				return; // Do not process other keys while editing a cell
			}

			// If no selection yet, ignore all keys except maybe enter
			if (!selectedCells) {
				return;
			}

			// Use logical cursor position for navigation if available, otherwise fall back to selection end
			const curRow = logicalCursorPosition?.row ?? selectedCells.end.row;
			const curCol = logicalCursorPosition?.col ?? selectedCells.end.col;

			let nextPosition: { row: number; col: number } | null = null;
			let handled = false;

			if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
				e.preventDefault();
				handled = true;

				// Find the next navigable position based on direction
				if (e.key === "ArrowUp") {
					nextPosition = findNextNavigableCell(curRow, curCol, "up");
				} else if (e.key === "ArrowDown") {
					nextPosition = findNextNavigableCell(curRow, curCol, "down");
				} else if (e.key === "ArrowLeft") {
					nextPosition = findNextNavigableCell(curRow, curCol, "left");
				} else if (e.key === "ArrowRight") {
					nextPosition = findNextNavigableCell(curRow, curCol, "right");
				}

				// If we found a valid next position, navigate to it
				if (nextPosition) {
					const nextRow = nextPosition.row;
					const nextCol = nextPosition.col;

					// Update the logical cursor position for future navigation
					setLogicalCursorPosition({ row: nextRow, col: nextCol });

					if (e.shiftKey) {
						// Extend the selection keeping the original start
						const rawSelection = selectedCells
							? {
									start: selectedCells.start,
									end: { row: nextRow, col: nextCol },
								}
							: {
									start: { row: curRow, col: curCol },
									end: { row: nextRow, col: nextCol },
								};

						// Expand selection to include full merged cells
						const expandedSelection = expandSelectionForMergedCells(
							tableProperties.cells,
							rawSelection,
							tableProperties.rows,
							tableProperties.columns,
						);

						setSelectedCells(expandedSelection);
					} else {
						// Move selection - also expand for single cell to handle merged cells
						const singleCellSelection = {
							start: { row: nextRow, col: nextCol },
							end: { row: nextRow, col: nextCol },
						};

						const expandedSingleSelection = expandSelectionForMergedCells(
							tableProperties.cells,
							singleCellSelection,
							tableProperties.rows,
							tableProperties.columns,
						);

						setSelectedCells(expandedSingleSelection);
					}
				}
			}

			if (e.key === "Enter" && isSingleCellSelected) {
				e.preventDefault();
				handled = true;
				// Start editing the focused cell - convert logical coordinates to cell array indices
				const cellPosition = logicalPositionToCell(
					tableProperties.cells,
					selectedCells.start.row,
					selectedCells.start.col,
					tableProperties.rows,
					tableProperties.columns,
				);
				if (cellPosition) {
					setEditingCell({
						row: cellPosition.rowIndex,
						col: cellPosition.colIndex,
					});
				}
				setTimeout(() => {
					const editorElement = document.querySelector(".ProseMirror");
					if (editorElement) {
						(editorElement as HTMLElement).focus();
					}
				}, 0);
			}

			if (!handled && e.key === "Escape") {
				e.preventDefault();
				setSelectedCells(null);
				setLogicalCursorPosition(null);
			}
		}

		window.addEventListener("keydown", handleKeyboardNavigation);
		return () =>
			window.removeEventListener("keydown", handleKeyboardNavigation);
	}, [
		editingCell,
		selectedCells,
		logicalCursorPosition,
		isSingleCellSelected,
		tableProperties.rows,
		tableProperties.columns,
		tableProperties.cells,
	]);

	return (
		<div
			ref={containerRef}
			className={`relative w-full h-full ${isDragging ? "select-none" : ""}`}
		>
			<table
				ref={tableRef}
				className="border-collapse table-fixed w-full h-full"
			>
				<tbody>
					{/* Use the shared helper to render the table body content */}
					<TableBodyContent
						tableProperties={tableProperties}
						rowHeightsMm={rowHeights}
						columnWidthsMm={columnWidths}
						renderCell={renderEditableCell}
					/>
				</tbody>
			</table>

			{!editingCell &&
				(!selectedCells || isSingleCellSelected) &&
				renderAddButtons()}
			{!editingCell && renderDeleteButtons()}
		</div>
	);
}
